<div class="p-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">QR Code Preview</h3>
        <button onclick="closeQRPreview()" class="text-gray-400 hover:text-gray-600">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>
    
    <div class="text-center">
        <!-- Item Information -->
        <div class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ supply_item.name }}</h4>
            <p class="text-sm text-gray-600 mb-1">{{ supply_item.category.name }}</p>
            <p class="text-sm text-gray-600">Stock: {{ supply_item.current_stock }} {{ supply_item.unit_of_measure }}</p>
        </div>
        
        <!-- QR Code Image -->
        {% if supply_item.qr_code %}
            <div class="mb-6">
                <img src="{{ supply_item.qr_code.url }}" 
                     alt="QR Code for {{ supply_item.name }}" 
                     class="mx-auto max-w-full h-auto border border-gray-200 rounded-lg">
            </div>
            
            <!-- QR Code Data -->
            <div class="mb-6 p-3 bg-gray-50 rounded-lg">
                <p class="text-xs text-gray-600 mb-1">QR Code Data:</p>
                <p class="text-sm font-mono text-gray-800 break-all">{{ supply_item.qr_code_data }}</p>
            </div>
        {% else %}
            <div class="mb-6 py-8">
                <i class="fas fa-qrcode text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-600">No QR code generated yet</p>
            </div>
        {% endif %}
        
        <!-- Action Buttons -->
        <div class="flex gap-3">
            {% if supply_item.qr_code %}
                <a href="{% url 'download_qr_code' supply_item.id %}" 
                   class="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-download mr-2"></i>Download
                </a>
                <button onclick="regenerateQRCode({{ supply_item.id }})" 
                        class="flex-1 bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors">
                    <i class="fas fa-sync mr-2"></i>Regenerate
                </button>
            {% else %}
                <button onclick="generateQRCode({{ supply_item.id }})" 
                        class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-qrcode mr-2"></i>Generate QR Code
                </button>
            {% endif %}
        </div>
    </div>
</div>