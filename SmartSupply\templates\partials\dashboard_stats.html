<!-- Dashboard Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Requests Today -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            {% if user_profile.role == 'department_user' %}
                                My Requests Today
                            {% else %}
                                Total Requests Today
                            {% endif %}
                        </dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.total_requests_today|default:0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Requests -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            {% if user_profile.role == 'gso_staff' %}
                                Pending Approvals
                            {% elif user_profile.role == 'department_user' %}
                                My Pending Requests
                            {% else %}
                                Pending Requests
                            {% endif %}
                        </dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.pending_requests|default:0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Approved Today -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            {% if user_profile.role == 'department_user' %}
                                My Approved Today
                            {% else %}
                                Approved Today
                            {% endif %}
                        </dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.approved_today|default:0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Items / Additional Stat -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    {% if user_profile.role == 'department_user' %}
                        <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    {% else %}
                        <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    {% endif %}
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            {% if user_profile.role == 'department_user' %}
                                Total Requests
                            {% else %}
                                Low Stock Items
                            {% endif %}
                        </dt>
                        <dd class="text-lg font-medium text-gray-900">
                            {% if user_profile.role == 'department_user' %}
                                {{ stats.total_requests|default:0 }}
                            {% else %}
                                {{ stats.low_stock_items|default:0 }}
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>