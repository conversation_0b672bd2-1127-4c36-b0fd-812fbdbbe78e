<!-- My Recent Requests Widget -->
<div class="space-y-3">
    {% if recent_requests %}
        {% for request in recent_requests %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 truncate">
                        {{ request.request_number }}
                    </div>
                    <div class="text-xs text-gray-500">
                        {{ request.request_date|date:"M d, Y" }} • {{ request.total_items }} item{{ request.total_items|pluralize }}
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                        {% if request.status == 'pending' %}
                            bg-yellow-100 text-yellow-800
                        {% elif request.status == 'approved' %}
                            bg-blue-100 text-blue-800
                        {% elif request.status == 'released' %}
                            bg-green-100 text-green-800
                        {% elif request.status == 'rejected' %}
                            bg-red-100 text-red-800
                        {% endif %}
                    ">
                        {{ request.get_status_display }}
                    </span>
                    <a href="{% url 'request_detail' request.pk %}" 
                       class="text-blue-600 hover:text-blue-900">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        {% endfor %}
        
        <div class="text-center pt-2">
            <a href="{% url 'request_list' %}" 
               class="text-sm text-blue-600 hover:text-blue-900 font-medium">
                View all requests →
            </a>
        </div>
    {% else %}
        <div class="text-center py-6">
            <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <p class="mt-2 text-sm text-gray-500">No requests yet</p>
            <a href="{% url 'create_request' %}" 
               class="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-900">
                Create your first request
            </a>
        </div>
    {% endif %}
</div>