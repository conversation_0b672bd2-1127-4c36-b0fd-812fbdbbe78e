{% extends 'base.html' %}
{% load static %}

{% block title %}Inventory Management{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Inventory Management</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage supply items, stock levels, and inventory transactions</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'low_stock_alerts' %}" 
                       class="inline-flex items-center px-4 py-2 border border-orange-300 rounded-md shadow-sm text-sm font-medium text-orange-700 bg-orange-50 hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        Low Stock Alerts
                        {% if low_stock_count > 0 %}
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                {{ low_stock_count }}
                            </span>
                        {% endif %}
                    </a>
                    <a href="{% url 'add_supply_item' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Item
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Items</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ total_items }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Low Stock</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ low_stock_count }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Out of Stock</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ out_of_stock_count }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter Form -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Search & Filter</h3>
            </div>
            <div class="p-6">
                <form method="get" class="space-y-4 md:space-y-0 md:grid md:grid-cols-4 md:gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" 
                               name="search" 
                               id="search"
                               value="{{ search_query }}"
                               placeholder="Search by name, description, or category..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               hx-get="{% url 'inventory_list_htmx' %}"
                               hx-trigger="keyup changed delay:300ms"
                               hx-target="#inventory-table"
                               hx-include="[name='category'], [name='stock_status'], [name='sort']">
                    </div>
                    
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <select name="category" 
                                id="category"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                hx-get="{% url 'inventory_list_htmx' %}"
                                hx-trigger="change"
                                hx-target="#inventory-table"
                                hx-include="[name='search'], [name='stock_status'], [name='sort']">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label for="stock_status" class="block text-sm font-medium text-gray-700 mb-1">Stock Status</label>
                        <select name="stock_status" 
                                id="stock_status"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                hx-get="{% url 'inventory_list_htmx' %}"
                                hx-trigger="change"
                                hx-target="#inventory-table"
                                hx-include="[name='search'], [name='category'], [name='sort']">
                            <option value="">All Items</option>
                            <option value="normal" {% if stock_filter == 'normal' %}selected{% endif %}>Normal Stock</option>
                            <option value="low" {% if stock_filter == 'low' %}selected{% endif %}>Low Stock</option>
                            <option value="out" {% if stock_filter == 'out' %}selected{% endif %}>Out of Stock</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                        <select name="sort" 
                                id="sort"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                hx-get="{% url 'inventory_list_htmx' %}"
                                hx-trigger="change"
                                hx-target="#inventory-table"
                                hx-include="[name='search'], [name='category'], [name='stock_status']">
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name (A-Z)</option>
                            <option value="category" {% if sort_by == 'category' %}selected{% endif %}>Category</option>
                            <option value="stock_asc" {% if sort_by == 'stock_asc' %}selected{% endif %}>Stock (Low to High)</option>
                            <option value="stock_desc" {% if sort_by == 'stock_desc' %}selected{% endif %}>Stock (High to Low)</option>
                            <option value="updated" {% if sort_by == 'updated' %}selected{% endif %}>Recently Updated</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="bg-white shadow rounded-lg">
            <div id="inventory-table">
                {% include 'inventory/partials/inventory_table.html' %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal Container -->
<div id="modal-container"></div>
{% endblock %}

{% block extra_js %}
<script>
    // Handle delete confirmation
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 200 && event.detail.requestConfig.verb === 'delete') {
            const response = JSON.parse(event.detail.xhr.responseText);
            if (response.success) {
                // Show success message
                showToast(response.message, 'success');
                // Refresh the inventory table
                htmx.trigger('#inventory-table', 'refresh');
            } else {
                showToast(response.message, 'error');
            }
        }
    });

    function showToast(message, type) {
        // Simple toast notification
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md shadow-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
</script>
{% endblock %}