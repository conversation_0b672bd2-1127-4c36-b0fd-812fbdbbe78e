#!/usr/bin/env python
"""
Simple test script to verify supply request functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.contrib.auth.models import User
from suptrack.models import UserProfile, SupplyCategory, SupplyItem, SupplyRequest, RequestItem
from django.db import transaction

def test_request_creation():
    """Test creating a supply request with items"""
    print("Testing supply request creation...")
    
    try:
        with transaction.atomic():
            # Create test user if not exists
            user, created = User.objects.get_or_create(
                username='testuser',
                defaults={
                    'first_name': 'Test',
                    'last_name': 'User',
                    'email': '<EMAIL>'
                }
            )
            
            # Create user profile
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'role': 'department_user',
                    'department': 'IT Department'
                }
            )
            
            # Create test category and items
            category, created = SupplyCategory.objects.get_or_create(
                name='Office Supplies',
                defaults={'description': 'General office supplies'}
            )
            
            item1, created = SupplyItem.objects.get_or_create(
                name='Ballpen',
                defaults={
                    'category': category,
                    'unit_of_measure': 'pieces',
                    'current_stock': 100,
                    'minimum_stock': 10
                }
            )
            
            item2, created = SupplyItem.objects.get_or_create(
                name='Bond Paper',
                defaults={
                    'category': category,
                    'unit_of_measure': 'reams',
                    'current_stock': 50,
                    'minimum_stock': 5
                }
            )
            
            # Create supply request
            request = SupplyRequest.objects.create(
                requester=user,
                department='IT Department',
                notes='Test request for office supplies'
            )
            
            # Add request items
            RequestItem.objects.create(
                request=request,
                supply_item=item1,
                quantity_requested=10
            )
            
            RequestItem.objects.create(
                request=request,
                supply_item=item2,
                quantity_requested=2
            )
            
            print(f"✓ Successfully created request: {request.request_number}")
            print(f"  - Requester: {request.requester.get_full_name()}")
            print(f"  - Department: {request.department}")
            print(f"  - Status: {request.get_status_display()}")
            print(f"  - Total items: {request.total_items}")
            print(f"  - Total quantity: {request.total_quantity}")
            
            # Test request items
            print("\n  Request Items:")
            for item in request.items.all():
                print(f"    - {item.supply_item.name}: {item.quantity_requested} {item.supply_item.unit_of_measure}")
            
            return True
            
    except Exception as e:
        print(f"✗ Error creating request: {str(e)}")
        return False

def test_request_queries():
    """Test request listing and filtering"""
    print("\nTesting request queries...")
    
    try:
        # Test basic queries
        total_requests = SupplyRequest.objects.count()
        pending_requests = SupplyRequest.objects.filter(status='pending').count()
        
        print(f"✓ Total requests: {total_requests}")
        print(f"✓ Pending requests: {pending_requests}")
        
        # Test recent requests
        recent_requests = SupplyRequest.objects.order_by('-request_date')[:5]
        print(f"✓ Recent requests: {recent_requests.count()}")
        
        for request in recent_requests:
            print(f"    - {request.request_number} ({request.get_status_display()})")
        
        return True
        
    except Exception as e:
        print(f"✗ Error querying requests: {str(e)}")
        return False

def test_model_methods():
    """Test model methods and properties"""
    print("\nTesting model methods...")
    
    try:
        request = SupplyRequest.objects.first()
        if request:
            print(f"✓ Request URL: {request.get_absolute_url()}")
            print(f"✓ Status color: {request.status_color}")
            print(f"✓ Total items: {request.total_items}")
            print(f"✓ Total quantity: {request.total_quantity}")
            
            # Test supply item properties
            for item in SupplyItem.objects.all()[:3]:
                print(f"✓ {item.name}: Stock={item.current_stock}, Low stock={item.is_low_stock}, Out of stock={item.is_out_of_stock}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing model methods: {str(e)}")
        return False

if __name__ == '__main__':
    print("=== Supply Request Functionality Test ===\n")
    
    success = True
    success &= test_request_creation()
    success &= test_request_queries()
    success &= test_model_methods()
    
    print(f"\n=== Test Results ===")
    if success:
        print("✓ All tests passed!")
    else:
        print("✗ Some tests failed!")
    
    sys.exit(0 if success else 1)