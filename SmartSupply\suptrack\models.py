from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
import uuid


class UserProfile(models.Model):
    """Extended user profile with role-based access control"""
    ROLE_CHOICES = [
        ('admin', 'Admin'),
        ('gso_staff', 'GSO Staff'),
        ('department_user', 'Department User'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    department = models.CharField(max_length=100, blank=True)
    phone_number = models.CharField(max_length=15, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"
    
    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"


class SupplyCategory(models.Model):
    """Categories for organizing supply items"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = "Supply Category"
        verbose_name_plural = "Supply Categories"
        ordering = ['name']


class SupplyItem(models.Model):
    """Individual supply items with QR code tracking"""
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    category = models.ForeignKey(SupplyCategory, on_delete=models.CASCADE, related_name='items')
    unit_of_measure = models.CharField(max_length=50)
    current_stock = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    minimum_stock = models.IntegerField(default=10, validators=[MinValueValidator(0)])
    qr_code = models.ImageField(upload_to='qr_codes/', blank=True)
    qr_code_data = models.CharField(max_length=255, unique=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.current_stock} {self.unit_of_measure})"
    
    def save(self, *args, **kwargs):
        if not self.qr_code_data:
            self.qr_code_data = str(uuid.uuid4())
        super().save(*args, **kwargs)
    
    @property
    def is_low_stock(self):
        return self.current_stock <= self.minimum_stock
    
    @property
    def is_out_of_stock(self):
        return self.current_stock == 0
    
    class Meta:
        verbose_name = "Supply Item"
        verbose_name_plural = "Supply Items"
        ordering = ['name']


class SupplyRequest(models.Model):
    """Supply requests from department users"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('released', 'Released'),
        ('rejected', 'Rejected'),
    ]
    
    request_number = models.CharField(max_length=20, unique=True, blank=True)
    requester = models.ForeignKey(User, on_delete=models.CASCADE, related_name='supply_requests')
    department = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    request_date = models.DateTimeField(auto_now_add=True)
    approved_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='approved_requests'
    )
    approved_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.request_number} - {self.requester.username} ({self.get_status_display()})"
    
    def save(self, *args, **kwargs):
        if not self.request_number:
            # Generate request number: REQ-YYYYMMDD-XXXX
            from django.utils import timezone
            date_str = timezone.now().strftime('%Y%m%d')
            last_request = SupplyRequest.objects.filter(
                request_number__startswith=f'REQ-{date_str}'
            ).order_by('-request_number').first()
            
            if last_request:
                last_num = int(last_request.request_number.split('-')[-1])
                new_num = last_num + 1
            else:
                new_num = 1
            
            self.request_number = f'REQ-{date_str}-{new_num:04d}'
        super().save(*args, **kwargs)
    
    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('request_detail', kwargs={'pk': self.pk})
    
    @property
    def total_items(self):
        return self.items.count()
    
    @property
    def total_quantity(self):
        return sum(item.quantity_requested for item in self.items.all())
    
    @property
    def status_color(self):
        colors = {
            'pending': 'yellow',
            'approved': 'blue',
            'released': 'green',
            'rejected': 'red'
        }
        return colors.get(self.status, 'gray')
    
    class Meta:
        verbose_name = "Supply Request"
        verbose_name_plural = "Supply Requests"
        ordering = ['-request_date']


class RequestItem(models.Model):
    """Individual items within a supply request"""
    request = models.ForeignKey(SupplyRequest, on_delete=models.CASCADE, related_name='items')
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    quantity_requested = models.IntegerField(validators=[MinValueValidator(1)])
    quantity_approved = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    quantity_released = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    
    def __str__(self):
        return f"{self.supply_item.name} - {self.quantity_requested} requested"
    
    @property
    def is_fully_approved(self):
        return self.quantity_approved >= self.quantity_requested
    
    @property
    def is_fully_released(self):
        return self.quantity_released >= self.quantity_approved
    
    class Meta:
        verbose_name = "Request Item"
        verbose_name_plural = "Request Items"
        unique_together = ['request', 'supply_item']


class QRScanLog(models.Model):
    """Log of QR code scans for tracking supply movement"""
    SCAN_TYPE_CHOICES = [
        ('issuance', 'Issuance'),
        ('return', 'Return'),
        ('inventory_check', 'Inventory Check'),
    ]
    
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, related_name='scan_logs')
    scanned_by = models.ForeignKey(User, on_delete=models.CASCADE)
    scan_type = models.CharField(max_length=20, choices=SCAN_TYPE_CHOICES)
    scan_datetime = models.DateTimeField(auto_now_add=True)
    location = models.CharField(max_length=200, blank=True)
    notes = models.TextField(blank=True)
    request_item = models.ForeignKey(
        RequestItem, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='scan_logs'
    )
    
    def __str__(self):
        return f"{self.supply_item.name} - {self.get_scan_type_display()} by {self.scanned_by.username}"
    
    class Meta:
        verbose_name = "QR Scan Log"
        verbose_name_plural = "QR Scan Logs"
        ordering = ['-scan_datetime']


class InventoryTransaction(models.Model):
    """Track all inventory movements and adjustments"""
    TRANSACTION_TYPE_CHOICES = [
        ('in', 'Stock In'),
        ('out', 'Stock Out'),
        ('adjustment', 'Adjustment'),
    ]
    
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    quantity = models.IntegerField()
    previous_stock = models.IntegerField()
    new_stock = models.IntegerField()
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction_date = models.DateTimeField(auto_now_add=True)
    reference_number = models.CharField(max_length=50, blank=True)
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.supply_item.name} - {self.get_transaction_type_display()} ({self.quantity})"
    
    class Meta:
        verbose_name = "Inventory Transaction"
        verbose_name_plural = "Inventory Transactions"
        ordering = ['-transaction_date']
