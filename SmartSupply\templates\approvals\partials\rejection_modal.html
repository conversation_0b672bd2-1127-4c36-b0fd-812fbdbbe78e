<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
     x-data="{ open: true }" 
     x-show="open"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        
        <!-- Modal Header -->
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Reject Request</h3>
                    <p class="text-sm text-gray-500">{{ request.request_number }}</p>
                </div>
            </div>
            <button @click="open = false" 
                    class="text-gray-400 hover:text-gray-600 focus:outline-none">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Request Details -->
        <div class="mb-4 p-3 bg-gray-50 rounded-md">
            <div class="text-sm">
                <div class="font-medium text-gray-900">{{ request.requester.get_full_name|default:request.requester.username }}</div>
                <div class="text-gray-500">{{ request.department }}</div>
                <div class="text-gray-500">{{ request.total_items }} item{{ request.total_items|pluralize }}, {{ request.total_quantity }} total quantity</div>
                <div class="text-gray-500">Requested: {{ request.request_date|date:"M d, Y g:i A" }}</div>
            </div>
        </div>
        
        <!-- Rejection Form -->
        <form hx-post="{% url 'reject_request_htmx' request.id %}"
              hx-target="#request-row-{{ request.id }}"
              hx-swap="outerHTML"
              hx-on::after-request="if(event.detail.successful) { document.getElementById('modal-container').innerHTML = ''; }">
            
            {% csrf_token %}
            
            <div class="mb-4">
                <label for="rejection_reason" class="block text-sm font-medium text-gray-700 mb-2">
                    Rejection Reason <span class="text-red-500">*</span>
                </label>
                <textarea name="rejection_reason" 
                          id="rejection_reason"
                          rows="4"
                          required
                          placeholder="Please provide a clear reason for rejecting this request..."
                          class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"></textarea>
                <p class="mt-1 text-xs text-gray-500">This reason will be visible to the requester.</p>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3">
                <button type="button" 
                        @click="open = false"
                        class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Cancel
                </button>
                <button type="submit"
                        class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Reject Request
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-focus the textarea when modal opens
setTimeout(() => {
    const textarea = document.getElementById('rejection_reason');
    if (textarea) {
        textarea.focus();
    }
}, 100);

// Handle escape key to close modal
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        document.getElementById('modal-container').innerHTML = '';
    }
});
</script>