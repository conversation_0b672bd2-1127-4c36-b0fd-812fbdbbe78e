<!-- Recent Activities -->
<div class="flow-root">
    <ul class="-mb-8">
        {% for activity in recent_activities %}
        <li>
            <div class="relative pb-8">
                {% if not forloop.last %}
                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                {% endif %}
                <div class="relative flex space-x-3">
                    <div>
                        {% if user_profile.role == 'gso_staff' %}
                            <span class="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center ring-8 ring-white">
                                <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                            </span>
                        {% else %}
                            <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                            </span>
                        {% endif %}
                    </div>
                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                            <p class="text-sm text-gray-500">{{ activity.description }}</p>
                        </div>
                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                            {{ activity.timestamp|timesince }} ago
                        </div>
                    </div>
                </div>
            </div>
        </li>
        {% empty %}
        <li class="text-center py-8 text-gray-500">
            {% if user_profile.role == 'gso_staff' %}
                No pending requests
            {% elif user_profile.role == 'admin' %}
                No recent system activity
            {% else %}
                No recent activity
            {% endif %}
        </li>
        {% endfor %}
    </ul>
</div>