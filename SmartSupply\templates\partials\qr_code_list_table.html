<!-- Table Header -->
<div class="px-6 py-4 border-b border-gray-200">
    <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-gray-900">Supply Items</h2>
        <div class="text-sm text-gray-600">
            <span id="selectedCount">0</span> selected
        </div>
    </div>
</div>

<!-- Table -->
<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="rounded">
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    QR Code
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in page_obj %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" 
                               class="item-checkbox rounded" 
                               value="{{ item.id }}"
                               data-has-qr="{% if item.qr_code %}true{% else %}false{% endif %}"
                               onchange="toggleItemSelection({{ item.id }})">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                                {% if item.description %}
                                    <div class="text-sm text-gray-500">{{ item.description|truncatechars:50 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {{ item.category.name }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ item.current_stock }} {{ item.unit_of_measure }}</div>
                        <div class="text-sm text-gray-500">
                            <span class="px-2 py-1 rounded-full text-xs {% if item.is_out_of_stock %}bg-red-100 text-red-800{% elif item.is_low_stock %}bg-yellow-100 text-yellow-800{% else %}bg-green-100 text-green-800{% endif %}">
                                {% if item.is_out_of_stock %}
                                    Out of Stock
                                {% elif item.is_low_stock %}
                                    Low Stock
                                {% else %}
                                    In Stock
                                {% endif %}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if item.qr_code %}
                            <div class="flex items-center">
                                <img src="{{ item.qr_code.url }}" alt="QR Code" class="w-8 h-8 object-contain mr-2">
                                <span class="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>Generated
                                </span>
                            </div>
                        {% else %}
                            <span class="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                                <i class="fas fa-times mr-1"></i>Not Generated
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            {% if item.qr_code %}
                                <button onclick="previewQRCode({{ item.id }})" 
                                        class="text-blue-600 hover:text-blue-900" title="Preview QR Code">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <a href="{% url 'download_qr_code' item.id %}" 
                                   class="text-green-600 hover:text-green-900" title="Download QR Code">
                                    <i class="fas fa-download"></i>
                                </a>
                                <button onclick="regenerateQRCode({{ item.id }})" 
                                        class="text-yellow-600 hover:text-yellow-900" title="Regenerate QR Code">
                                    <i class="fas fa-sync"></i>
                                </button>
                            {% else %}
                                <button onclick="generateQRCode({{ item.id }})" 
                                        class="text-green-600 hover:text-green-900" title="Generate QR Code">
                                    <i class="fas fa-qrcode"></i>
                                </button>
                            {% endif %}
                            <a href="{% url 'supply_item_detail' item.id %}" 
                               class="text-gray-600 hover:text-gray-900" title="View Details">
                                <i class="fas fa-info-circle"></i>
                            </a>
                        </div>
                    </td>
                </tr>
            {% empty %}
                <tr>
                    <td colspan="6" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                        <div class="py-8">
                            <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                            <p class="text-lg">No items found</p>
                            <p class="text-sm">Try adjusting your search criteria</p>
                        </div>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
            </div>
            <div class="flex space-x-2">
                {% if page_obj.has_previous %}
                    <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if qr_status_filter %}&qr_status={{ qr_status_filter }}{% endif %}" 
                       class="px-3 py-2 text-sm text-gray-600 hover:text-gray-900">First</a>
                    <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if qr_status_filter %}&qr_status={{ qr_status_filter }}{% endif %}" 
                       class="px-3 py-2 text-sm text-gray-600 hover:text-gray-900">Previous</a>
                {% endif %}
                
                <span class="px-3 py-2 text-sm text-gray-900 bg-gray-100 rounded">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if qr_status_filter %}&qr_status={{ qr_status_filter }}{% endif %}" 
                       class="px-3 py-2 text-sm text-gray-600 hover:text-gray-900">Next</a>
                    <a href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if qr_status_filter %}&qr_status={{ qr_status_filter }}{% endif %}" 
                       class="px-3 py-2 text-sm text-gray-600 hover:text-gray-900">Last</a>
                {% endif %}
            </div>
        </div>
    </div>
{% endif %}