{% extends 'base.html' %}
{% load static %}

{% block title %}{{ supply_item.name }} - Supply Item Details{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ supply_item.name }}</h1>
                    <p class="mt-1 text-sm text-gray-600">Supply item details and transaction history</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'inventory_list' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Inventory
                    </a>
                    <a href="{% url 'edit_supply_item' supply_item.pk %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Item
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Item Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Item Information</h3>
                    </div>
                    <div class="px-6 py-4 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Name</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ supply_item.name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Category</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ supply_item.category.name }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Unit of Measure</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ supply_item.unit_of_measure }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">QR Code Data</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ supply_item.qr_code_data }}</dd>
                            </div>
                        </div>
                        
                        {% if supply_item.description %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ supply_item.description }}</dd>
                            </div>
                        {% endif %}
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ supply_item.created_at|date:"M d, Y g:i A" }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ supply_item.updated_at|date:"M d, Y g:i A" }}</dd>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Recent Transactions</h3>
                        <a href="{% url 'inventory_transactions' %}?item={{ supply_item.pk }}" 
                           class="text-sm text-blue-600 hover:text-blue-900">
                            View All
                        </a>
                    </div>
                    <div class="overflow-hidden">
                        {% if transactions %}
                            <ul class="divide-y divide-gray-200">
                                {% for transaction in transactions %}
                                    <li class="px-6 py-4">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    {% if transaction.transaction_type == 'in' %}
                                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                            </svg>
                                                        </div>
                                                    {% elif transaction.transaction_type == 'out' %}
                                                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                                            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                                            </svg>
                                                        </div>
                                                    {% else %}
                                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                            </svg>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        {{ transaction.get_transaction_type_display }}
                                                        {% if transaction.transaction_type == 'in' %}+{% elif transaction.transaction_type == 'out' %}-{% endif %}{{ transaction.quantity }}
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        {{ transaction.previous_stock }} → {{ transaction.new_stock }} {{ supply_item.unit_of_measure }}
                                                    </div>
                                                    {% if transaction.notes %}
                                                        <div class="text-xs text-gray-400 mt-1">{{ transaction.notes|truncatechars:50 }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-sm text-gray-900">{{ transaction.performed_by.get_full_name|default:transaction.performed_by.username }}</div>
                                                <div class="text-xs text-gray-500">{{ transaction.transaction_date|date:"M d, Y g:i A" }}</div>
                                            </div>
                                        </div>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            <div class="px-6 py-12 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions yet</h3>
                                <p class="mt-1 text-sm text-gray-500">Transaction history will appear here once stock movements occur.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Recent QR Scans -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent QR Scans</h3>
                    </div>
                    <div class="overflow-hidden">
                        {% if scan_logs %}
                            <ul class="divide-y divide-gray-200">
                                {% for scan in scan_logs %}
                                    <li class="px-6 py-4">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        {{ scan.get_scan_type_display }}
                                                    </div>
                                                    {% if scan.location %}
                                                        <div class="text-sm text-gray-500">Location: {{ scan.location }}</div>
                                                    {% endif %}
                                                    {% if scan.notes %}
                                                        <div class="text-xs text-gray-400 mt-1">{{ scan.notes|truncatechars:50 }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-sm text-gray-900">{{ scan.scanned_by.get_full_name|default:scan.scanned_by.username }}</div>
                                                <div class="text-xs text-gray-500">{{ scan.scan_datetime|date:"M d, Y g:i A" }}</div>
                                            </div>
                                        </div>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            <div class="px-6 py-12 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No QR scans yet</h3>
                                <p class="mt-1 text-sm text-gray-500">QR scan history will appear here once the item is scanned.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Stock Status -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Stock Status</h3>
                    </div>
                    <div class="px-6 py-4 space-y-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gray-900">{{ supply_item.current_stock }}</div>
                            <div class="text-sm text-gray-500">{{ supply_item.unit_of_measure }} in stock</div>
                        </div>
                        
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            {% if supply_item.current_stock > 0 %}
                                {% widthratio supply_item.current_stock supply_item.minimum_stock 100 as stock_percentage %}
                                {% if stock_percentage > 100 %}
                                    <div class="bg-green-600 h-3 rounded-full" style="width: 100%"></div>
                                {% elif stock_percentage > 50 %}
                                    <div class="bg-yellow-400 h-3 rounded-full" style="width: {{ stock_percentage }}%"></div>
                                {% else %}
                                    <div class="bg-red-500 h-3 rounded-full" style="width: {{ stock_percentage }}%"></div>
                                {% endif %}
                            {% else %}
                                <div class="bg-red-500 h-3 rounded-full" style="width: 5%"></div>
                            {% endif %}
                        </div>
                        
                        <div class="flex justify-between text-sm text-gray-500">
                            <span>0</span>
                            <span>{{ supply_item.minimum_stock }} min</span>
                        </div>
                        
                        <div class="text-center">
                            {% if supply_item.is_out_of_stock %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                    Out of Stock
                                </span>
                            {% elif supply_item.is_low_stock %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    Low Stock
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    In Stock
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- QR Code -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">QR Code</h3>
                    </div>
                    <div class="px-6 py-4 text-center">
                        {% if supply_item.qr_code %}
                            <img src="{{ supply_item.qr_code.url }}" alt="QR Code" class="mx-auto h-32 w-32 border border-gray-300 rounded mb-3">
                            <p class="text-xs text-gray-500 font-mono mb-3 break-all">{{ supply_item.qr_code_data }}</p>
                            
                            <div class="space-y-2">
                                <a href="{% url 'download_qr_code' supply_item.id %}"
                                   class="w-full inline-flex justify-center items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-download mr-2"></i>Download QR Code
                                </a>
                                
                                <button onclick="regenerateQRCode({{ supply_item.id }})"
                                        class="w-full inline-flex justify-center items-center px-3 py-2 border border-yellow-300 rounded-md text-sm font-medium text-yellow-700 bg-yellow-50 hover:bg-yellow-100">
                                    <i class="fas fa-sync mr-2"></i>Regenerate QR Code
                                </button>
                                
                                <a href="{% url 'qr_code_management' %}"
                                   class="w-full inline-flex justify-center items-center px-3 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100">
                                    <i class="fas fa-cog mr-2"></i>QR Management
                                </a>
                            </div>
                        {% else %}
                            <div class="py-8">
                                <i class="fas fa-qrcode text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-600 mb-4">No QR code generated</p>
                                <button onclick="generateQRCode({{ supply_item.id }})"
                                        class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                                    <i class="fas fa-qrcode mr-2"></i>Generate QR Code
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Actions</h3>
                    </div>
                    <div class="px-6 py-4 space-y-3">
                        <a href="{% url 'stock_adjustment' supply_item.pk %}" 
                           class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM6 6v12h8V6H6zm3 3a1 1 0 112 0v6a1 1 0 11-2 0V9z"></path>
                            </svg>
                            Adjust Stock
                        </a>
                        
                        <a href="{% url 'edit_supply_item' supply_item.pk %}" 
                           class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Item
                        </a>
                        
                        <button hx-get="{% url 'delete_confirmation_modal' supply_item.pk %}"
                                hx-target="#modal-container"
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Item
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Container -->
<div id="modal-container"></div>
{% endblock %}

{% block extra_js %}
<script>
    // Handle delete confirmation
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 200 && event.detail.requestConfig.verb === 'delete') {
            const response = JSON.parse(event.detail.xhr.responseText);
            if (response.success) {
                // Redirect to inventory list after successful deletion
                window.location.href = "{% url 'inventory_list' %}";
            } else {
                showToast(response.message, 'error');
            }
        }
    });

    function showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md shadow-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }

    function generateQRCode(itemId) {
        fetch(`/qr-codes/generate/${itemId}/`, {
            method: 'POST',
            headers: {
                'HX-Request': 'true',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                location.reload(); // Refresh to show the new QR code
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Failed to generate QR code', 'error');
        });
    }

    function regenerateQRCode(itemId) {
        if (!confirm('Are you sure you want to regenerate the QR code? This will create a new QR code and invalidate the old one.')) {
            return;
        }
        
        fetch(`/qr-codes/regenerate/${itemId}/`, {
            method: 'POST',
            headers: {
                'HX-Request': 'true',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                location.reload(); // Refresh to show the new QR code
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Failed to regenerate QR code', 'error');
        });
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
</script>
{% endblock %}