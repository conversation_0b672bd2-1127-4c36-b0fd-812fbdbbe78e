from functools import wraps
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.http import HttpResponseForbidden, JsonResponse
from .models import UserProfile


def role_required(allowed_roles):
    """
    Decorator that requires user to have one of the specified roles.
    
    Args:
        allowed_roles: List of role strings or single role string
    
    Usage:
        @role_required(['admin', 'gso_staff'])
        def my_view(request):
            pass
        
        @role_required('admin')
        def admin_only_view(request):
            pass
    """
    if isinstance(allowed_roles, str):
        allowed_roles = [allowed_roles]
    
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            try:
                user_profile = request.user.userprofile
                if user_profile.role in allowed_roles:
                    return view_func(request, *args, **kwargs)
                else:
                    # Handle HTMX requests differently
                    if request.headers.get('HX-Request'):
                        return JsonResponse({
                            'success': False,
                            'message': 'Access denied. Insufficient privileges.',
                            'error_code': 'INSUFFICIENT_PRIVILEGES'
                        }, status=403)
                    
                    messages.error(request, 'Access denied. You do not have permission to access this page.')
                    return redirect('dashboard')
            except UserProfile.DoesNotExist:
                # Create default profile for users without one
                UserProfile.objects.create(
                    user=request.user,
                    role='department_user'
                )
                
                if 'department_user' in allowed_roles:
                    return view_func(request, *args, **kwargs)
                else:
                    if request.headers.get('HX-Request'):
                        return JsonResponse({
                            'success': False,
                            'message': 'Access denied. Insufficient privileges.',
                            'error_code': 'INSUFFICIENT_PRIVILEGES'
                        }, status=403)
                    
                    messages.error(request, 'Access denied. You do not have permission to access this page.')
                    return redirect('dashboard')
        
        return _wrapped_view
    return decorator


def admin_required(view_func):
    """Decorator that requires admin role"""
    return role_required('admin')(view_func)


def gso_staff_required(view_func):
    """Decorator that requires GSO staff role"""
    return role_required('gso_staff')(view_func)


def admin_or_gso_required(view_func):
    """Decorator that requires admin or GSO staff role"""
    return role_required(['admin', 'gso_staff'])(view_func)


def department_user_required(view_func):
    """Decorator that requires department user role"""
    return role_required('department_user')(view_func)


def any_authenticated_user(view_func):
    """Decorator that allows any authenticated user"""
    return role_required(['admin', 'gso_staff', 'department_user'])(view_func)


class RoleRequiredMixin:
    """
    Mixin for class-based views that requires specific roles.
    
    Usage:
        class MyView(RoleRequiredMixin, View):
            allowed_roles = ['admin', 'gso_staff']
            
            def get(self, request):
                return render(request, 'my_template.html')
    """
    allowed_roles = []
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        try:
            user_profile = request.user.userprofile
            if user_profile.role not in self.allowed_roles:
                return self.handle_insufficient_permission()
        except UserProfile.DoesNotExist:
            # Create default profile for users without one
            UserProfile.objects.create(
                user=request.user,
                role='department_user'
            )
            
            if 'department_user' not in self.allowed_roles:
                return self.handle_insufficient_permission()
        
        return super().dispatch(request, *args, **kwargs)
    
    def handle_no_permission(self):
        """Handle case when user is not authenticated"""
        from django.contrib.auth.views import redirect_to_login
        return redirect_to_login(self.request.get_full_path())
    
    def handle_insufficient_permission(self):
        """Handle case when user doesn't have required role"""
        # Handle HTMX requests differently
        if self.request.headers.get('HX-Request'):
            return JsonResponse({
                'success': False,
                'message': 'Access denied. Insufficient privileges.',
                'error_code': 'INSUFFICIENT_PRIVILEGES'
            }, status=403)
        
        messages.error(self.request, 'Access denied. You do not have permission to access this page.')
        return redirect('dashboard')


class AdminRequiredMixin(RoleRequiredMixin):
    """Mixin that requires admin role"""
    allowed_roles = ['admin']


class GSOStaffRequiredMixin(RoleRequiredMixin):
    """Mixin that requires GSO staff role"""
    allowed_roles = ['gso_staff']


class AdminOrGSORequiredMixin(RoleRequiredMixin):
    """Mixin that requires admin or GSO staff role"""
    allowed_roles = ['admin', 'gso_staff']


class DepartmentUserRequiredMixin(RoleRequiredMixin):
    """Mixin that requires department user role"""
    allowed_roles = ['department_user']


class AnyAuthenticatedUserMixin(RoleRequiredMixin):
    """Mixin that allows any authenticated user"""
    allowed_roles = ['admin', 'gso_staff', 'department_user']


def get_user_role(user):
    """
    Utility function to get user role safely.
    
    Args:
        user: Django User instance
    
    Returns:
        str: User role or 'department_user' as default
    """
    try:
        return user.userprofile.role
    except (AttributeError, UserProfile.DoesNotExist):
        return 'department_user'


def has_role(user, role):
    """
    Check if user has specific role.
    
    Args:
        user: Django User instance
        role: Role string to check
    
    Returns:
        bool: True if user has the role, False otherwise
    """
    return get_user_role(user) == role


def has_any_role(user, roles):
    """
    Check if user has any of the specified roles.
    
    Args:
        user: Django User instance
        roles: List of role strings to check
    
    Returns:
        bool: True if user has any of the roles, False otherwise
    """
    user_role = get_user_role(user)
    return user_role in roles


def is_admin(user):
    """Check if user is admin"""
    return has_role(user, 'admin')


def is_gso_staff(user):
    """Check if user is GSO staff"""
    return has_role(user, 'gso_staff')


def is_department_user(user):
    """Check if user is department user"""
    return has_role(user, 'department_user')


def can_manage_inventory(user):
    """Check if user can manage inventory (admin or GSO staff)"""
    return has_any_role(user, ['admin', 'gso_staff'])


def can_approve_requests(user):
    """Check if user can approve requests (admin or GSO staff)"""
    return has_any_role(user, ['admin', 'gso_staff'])


def can_scan_qr_codes(user):
    """Check if user can scan QR codes (GSO staff)"""
    return has_role(user, 'gso_staff')


def can_view_reports(user):
    """Check if user can view reports (admin)"""
    return has_role(user, 'admin')