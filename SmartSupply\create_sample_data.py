#!/usr/bin/env python
"""
Create sample data for testing dashboard functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.contrib.auth.models import User
from suptrack.models import *
from django.utils import timezone
from django.db.models import F
import uuid

def create_sample_data():
    """Create sample data for testing"""
    
    # Create some sample supply categories and items
    category = SupplyCategory.objects.get_or_create(
        name='Office Supplies', 
        defaults={'description': 'General office supplies'}
    )[0]
    
    category2 = SupplyCategory.objects.get_or_create(
        name='IT Equipment', 
        defaults={'description': 'Information technology equipment'}
    )[0]

    # Create sample supply items
    item1 = SupplyItem.objects.get_or_create(
        name='A4 Paper',
        defaults={
            'description': 'White A4 printing paper',
            'category': category,
            'unit_of_measure': 'Ream',
            'current_stock': 5,
            'minimum_stock': 10,
            'qr_code_data': str(uuid.uuid4())
        }
    )[0]

    item2 = SupplyItem.objects.get_or_create(
        name='Ballpoint Pens',
        defaults={
            'description': 'Blue ballpoint pens',
            'category': category,
            'unit_of_measure': 'Box',
            'current_stock': 25,
            'minimum_stock': 15,
            'qr_code_data': str(uuid.uuid4())
        }
    )[0]

    item3 = SupplyItem.objects.get_or_create(
        name='USB Flash Drive',
        defaults={
            'description': '32GB USB 3.0 flash drive',
            'category': category2,
            'unit_of_measure': 'Piece',
            'current_stock': 2,
            'minimum_stock': 5,
            'qr_code_data': str(uuid.uuid4())
        }
    )[0]

    # Create sample supply requests
    dept_user = User.objects.get(username='dept_user')
    request1 = SupplyRequest.objects.get_or_create(
        request_number='REQ-001',
        defaults={
            'requester': dept_user,
            'department': 'IT Department',
            'status': 'pending',
            'notes': 'Urgent request for office supplies'
        }
    )[0]

    request2 = SupplyRequest.objects.get_or_create(
        request_number='REQ-002',
        defaults={
            'requester': dept_user,
            'department': 'IT Department',
            'status': 'approved',
            'approved_date': timezone.now(),
            'notes': 'Regular monthly supplies'
        }
    )[0]

    # Create request items
    RequestItem.objects.get_or_create(
        request=request1,
        supply_item=item1,
        defaults={'quantity_requested': 5}
    )

    RequestItem.objects.get_or_create(
        request=request1,
        supply_item=item2,
        defaults={'quantity_requested': 2}
    )

    RequestItem.objects.get_or_create(
        request=request2,
        supply_item=item3,
        defaults={'quantity_requested': 3, 'quantity_approved': 2}
    )

    print('Sample data created successfully!')
    print(f'Supply Items: {SupplyItem.objects.count()}')
    print(f'Supply Requests: {SupplyRequest.objects.count()}')
    print(f'Low stock items: {SupplyItem.objects.filter(current_stock__lte=F("minimum_stock")).count()}')

if __name__ == '__main__':
    create_sample_data()