#!/usr/bin/env python
"""
Test dashboard content and role-specific functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from suptrack.models import UserProfile, SupplyRequest, SupplyItem
from django.db.models import F

def test_dashboard_content():
    """Test that dashboard shows correct role-specific content"""
    client = Client()
    
    # Test admin dashboard
    admin_user = User.objects.get(username='admin')
    client.force_login(admin_user)
    
    response = client.get('/htmx/dashboard-stats/')
    content = response.content.decode()
    print("=== ADMIN DASHBOARD STATS ===")
    print("✓ Admin can see total requests" if "Total Requests Today" in content else "✗ Missing total requests")
    print("✓ Admin can see pending requests" if "Pending Requests" in content else "✗ Missing pending requests")
    print("✓ Admin can see low stock items" if "Low Stock Items" in content else "✗ Missing low stock items")
    
    response = client.get('/htmx/dashboard-widgets/')
    content = response.content.decode()
    print("✓ Admin has User Management widget" if "User Management" in content else "✗ Missing User Management widget")
    print("✓ Admin has System Reports widget" if "System Reports" in content else "✗ Missing System Reports widget")
    print("✓ Admin has System Settings widget" if "System Settings" in content else "✗ Missing System Settings widget")
    
    # Test GSO staff dashboard
    gso_user = User.objects.get(username='gso_staff')
    client.force_login(gso_user)
    
    response = client.get('/htmx/dashboard-stats/')
    content = response.content.decode()
    print("\n=== GSO STAFF DASHBOARD STATS ===")
    print("✓ GSO can see pending approvals" if "Pending Approvals" in content else "✗ Missing pending approvals")
    print("✓ GSO can see approved today" if "Approved Today" in content else "✗ Missing approved today")
    print("✓ GSO can see low stock items" if "Low Stock Items" in content else "✗ Missing low stock items")
    
    response = client.get('/htmx/dashboard-widgets/')
    content = response.content.decode()
    print("✓ GSO has Approve Requests widget" if "Approve Requests" in content else "✗ Missing Approve Requests widget")
    print("✓ GSO has QR Scanner widget" if "QR Scanner" in content else "✗ Missing QR Scanner widget")
    print("✓ GSO has Inventory Management widget" if "Inventory Management" in content else "✗ Missing Inventory Management widget")
    
    # Test department user dashboard
    dept_user = User.objects.get(username='dept_user')
    client.force_login(dept_user)
    
    response = client.get('/htmx/dashboard-stats/')
    content = response.content.decode()
    print("\n=== DEPARTMENT USER DASHBOARD STATS ===")
    print("✓ Dept user can see my requests today" if "My Requests Today" in content else "✗ Missing my requests today")
    print("✓ Dept user can see my pending requests" if "My Pending Requests" in content else "✗ Missing my pending requests")
    print("✓ Dept user can see my approved today" if "My Approved Today" in content else "✗ Missing my approved today")
    print("✓ Dept user can see total requests" if "Total Requests" in content else "✗ Missing total requests")
    
    response = client.get('/htmx/dashboard-widgets/')
    content = response.content.decode()
    print("✓ Dept user has Create Request widget" if "Create Request" in content else "✗ Missing Create Request widget")
    print("✓ Dept user has My Requests widget" if "My Requests" in content else "✗ Missing My Requests widget")
    
    # Test activities
    response = client.get('/htmx/dashboard-activities/')
    content = response.content.decode()
    print("✓ Dept user can see recent activities" if "recent activity" in content.lower() or "request" in content.lower() else "✗ Missing recent activities")
    
    # Print actual statistics
    print("\n=== ACTUAL STATISTICS ===")
    total_requests = SupplyRequest.objects.count()
    pending_requests = SupplyRequest.objects.filter(status='pending').count()
    low_stock_items = SupplyItem.objects.filter(current_stock__lte=F('minimum_stock')).count()
    
    print(f"Total requests in system: {total_requests}")
    print(f"Pending requests: {pending_requests}")
    print(f"Low stock items: {low_stock_items}")
    
    dept_user_requests = SupplyRequest.objects.filter(requester=dept_user).count()
    dept_user_pending = SupplyRequest.objects.filter(requester=dept_user, status='pending').count()
    
    print(f"Department user total requests: {dept_user_requests}")
    print(f"Department user pending requests: {dept_user_pending}")

if __name__ == '__main__':
    test_dashboard_content()
    print("\nDashboard content tests completed!")