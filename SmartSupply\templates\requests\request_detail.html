{% extends 'base.html' %}
{% load static %}

{% block title %}Request {{ request.request_number }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ request.request_number }}</h1>
                    <p class="mt-1 text-sm text-gray-600">Supply request details</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'request_list' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Requests
                    </a>
                    
                    {% if can_edit %}
                        <a href="{% url 'edit_request' request.pk %}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Request
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="mb-6">
                {% for message in messages %}
                    <div class="bg-{{ message.tags == 'error' and 'red' or 'green' }}-100 border border-{{ message.tags == 'error' and 'red' or 'green' }}-400 text-{{ message.tags == 'error' and 'red' or 'green' }}-700 px-4 py-3 rounded mb-4">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Request Information -->
                <div class="bg-white shadow rounded-lg mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Request Information</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Request Number</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.request_number }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1" 
                                    hx-get="{% url 'request_status_update_htmx' request.pk %}" 
                                    hx-trigger="every 30s"
                                    hx-swap="innerHTML">
                                    {% include 'requests/partials/status_badge.html' %}
                                </dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Requester</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    {{ request.requester.get_full_name|default:request.requester.username }}
                                </dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Department</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.department }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Request Date</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    {{ request.request_date|date:"M d, Y g:i A" }}
                                </dd>
                            </div>
                            
                            {% if request.approved_by %}
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Approved By</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ request.approved_by.get_full_name|default:request.approved_by.username }}
                                    </dd>
                                </div>
                            {% endif %}
                            
                            {% if request.approved_date %}
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Approved Date</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        {{ request.approved_date|date:"M d, Y g:i A" }}
                                    </dd>
                                </div>
                            {% endif %}
                        </dl>
                        
                        {% if request.notes %}
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500">Notes</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.notes }}</dd>
                            </div>
                        {% endif %}
                        
                        {% if request.rejection_reason %}
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500">Rejection Reason</dt>
                                <dd class="mt-1 text-sm text-red-600">{{ request.rejection_reason }}</dd>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Request Items -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Requested Items</h3>
                    </div>
                    <div class="overflow-hidden">
                        <!-- Desktop Table -->
                        <div class="hidden sm:block">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Item
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Requested
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Approved
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Released
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    {% for item in request.items.all %}
                                        <tr>
                                            <td class="px-6 py-4">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ item.supply_item.name }}
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    {{ item.supply_item.category.name }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ item.quantity_requested }} {{ item.supply_item.unit_of_measure }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ item.quantity_approved }} {{ item.supply_item.unit_of_measure }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ item.quantity_released }} {{ item.supply_item.unit_of_measure }}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Mobile Cards -->
                        <div class="sm:hidden">
                            {% for item in request.items.all %}
                                <div class="border-b border-gray-200 p-4">
                                    <div class="text-sm font-medium text-gray-900 mb-2">
                                        {{ item.supply_item.name }}
                                    </div>
                                    <div class="text-sm text-gray-500 mb-3">
                                        {{ item.supply_item.category.name }}
                                    </div>
                                    <div class="grid grid-cols-3 gap-4 text-sm">
                                        <div>
                                            <div class="text-gray-500">Requested</div>
                                            <div class="font-medium">{{ item.quantity_requested }} {{ item.supply_item.unit_of_measure }}</div>
                                        </div>
                                        <div>
                                            <div class="text-gray-500">Approved</div>
                                            <div class="font-medium">{{ item.quantity_approved }} {{ item.supply_item.unit_of_measure }}</div>
                                        </div>
                                        <div>
                                            <div class="text-gray-500">Released</div>
                                            <div class="font-medium">{{ item.quantity_released }} {{ item.supply_item.unit_of_measure }}</div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Actions -->
                <div class="bg-white shadow rounded-lg mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Actions</h3>
                    </div>
                    <div class="px-6 py-4 space-y-3">
                        {% if can_edit %}
                            <a href="{% url 'edit_request' request.pk %}" 
                               class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Edit Request
                            </a>
                            
                            <button type="button"
                                    hx-delete="{% url 'delete_request' request.pk %}"
                                    hx-confirm="Are you sure you want to delete this request? This action cannot be undone."
                                    hx-target="body"
                                    class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete Request
                            </button>
                        {% endif %}
                        
                        {% if can_approve and request.status == 'pending' %}
                            <a href="{% url 'approve_request' request.pk %}" 
                               class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Approve Request
                            </a>
                        {% endif %}
                    </div>
                </div>

                <!-- Request Summary -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Summary</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Items</dt>
                                <dd class="mt-1 text-2xl font-semibold text-gray-900">{{ request.total_items }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Quantity</dt>
                                <dd class="mt-1 text-2xl font-semibold text-gray-900">{{ request.total_quantity }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Days Since Request</dt>
                                <dd class="mt-1 text-2xl font-semibold text-gray-900">
                                    {{ request.request_date|timesince|truncatewords:1 }}
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}