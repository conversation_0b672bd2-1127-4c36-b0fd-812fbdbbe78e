#!/usr/bin/env python
"""
Test script for dashboard widgets
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from suptrack.models import UserProfile, SupplyCategory, SupplyItem

def test_dashboard_widgets():
    """Test dashboard widgets functionality"""
    
    print("🧪 Testing Dashboard Widgets")
    print("=" * 50)
    
    # Create test client
    client = Client()
    
    # Create admin user
    admin_user, created = User.objects.get_or_create(
        username='admin_test',
        defaults={
            'first_name': 'Admin',
            'last_name': 'User',
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
    
    # Create user profile
    admin_profile, created = UserProfile.objects.get_or_create(
        user=admin_user,
        defaults={
            'role': 'admin',
            'department': 'Administration'
        }
    )
    
    # Login as admin
    client.login(username='admin_test', password='admin123')
    
    print(f"\n1. Testing as Admin user...")
    print(f"   User: {admin_user.username}")
    print(f"   Role: {admin_profile.role}")
    
    # Test dashboard widgets HTMX endpoint
    try:
        widgets_url = reverse('dashboard_widgets_htmx')
        response = client.get(widgets_url, HTTP_HX_REQUEST='true')
        print(f"✓ Dashboard Widgets HTMX: {widgets_url} (Status: {response.status_code})")
        
        # Check response content
        content = response.content.decode('utf-8')
        if 'Inventory Management' in content:
            print("✓ Inventory Management widget found in HTMX response")
        else:
            print("✗ Inventory Management widget not found in HTMX response")
            print("Response content preview:")
            print(content[:500] + "..." if len(content) > 500 else content)
            
    except Exception as e:
        print(f"✗ Dashboard Widgets HTMX: Error - {str(e)}")
    
    # Test GSO Staff user
    print(f"\n2. Testing as GSO Staff user...")
    
    # Create GSO user
    gso_user, created = User.objects.get_or_create(
        username='gso_test',
        defaults={
            'first_name': 'GSO',
            'last_name': 'Staff',
            'email': '<EMAIL>'
        }
    )
    if created:
        gso_user.set_password('gso123')
        gso_user.save()
    
    # Create user profile
    gso_profile, created = UserProfile.objects.get_or_create(
        user=gso_user,
        defaults={
            'role': 'gso_staff',
            'department': 'GSO'
        }
    )
    
    # Login as GSO staff
    client.login(username='gso_test', password='gso123')
    
    print(f"   User: {gso_user.username}")
    print(f"   Role: {gso_profile.role}")
    
    try:
        widgets_url = reverse('dashboard_widgets_htmx')
        response = client.get(widgets_url, HTTP_HX_REQUEST='true')
        print(f"✓ Dashboard Widgets HTMX: {widgets_url} (Status: {response.status_code})")
        
        # Check response content
        content = response.content.decode('utf-8')
        if 'Inventory Management' in content:
            print("✓ Inventory Management widget found in GSO HTMX response")
        else:
            print("✗ Inventory Management widget not found in GSO HTMX response")
            print("Response content preview:")
            print(content[:500] + "..." if len(content) > 500 else content)
            
    except Exception as e:
        print(f"✗ Dashboard Widgets HTMX: Error - {str(e)}")
    
    print("\n" + "=" * 50)
    print("✅ Dashboard Widgets Test Complete!")

if __name__ == '__main__':
    test_dashboard_widgets()