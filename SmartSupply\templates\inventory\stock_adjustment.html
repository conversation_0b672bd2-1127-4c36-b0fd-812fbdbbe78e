{% extends 'base.html' %}
{% load static %}

{% block title %}Adjust Stock - {{ supply_item.name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Adjust Stock</h1>
                    <p class="mt-1 text-sm text-gray-600">Update stock levels for {{ supply_item.name }}</p>
                </div>
                <div>
                    <a href="{% url 'supply_item_detail' supply_item.pk %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Item
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Form -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow rounded-lg">
                    <form method="post" class="space-y-6">
                        {% csrf_token %}
                        
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Stock Adjustment</h3>
                            <p class="mt-1 text-sm text-gray-600">
                                Record stock movements for accurate inventory tracking
                            </p>
                        </div>

                        <div class="px-6 pb-6 space-y-6">
                            <!-- Transaction Type -->
                            <div>
                                <label for="{{ form.transaction_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Transaction Type *
                                </label>
                                <div class="mt-1">
                                    {{ form.transaction_type }}
                                </div>
                                {% if form.transaction_type.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.transaction_type.errors.0 }}
                                    </div>
                                {% endif %}
                                <p class="mt-1 text-sm text-gray-500">
                                    Select the type of stock movement
                                </p>
                            </div>

                            <!-- Quantity -->
                            <div>
                                <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Quantity *
                                </label>
                                <div class="mt-1 relative">
                                    {{ form.quantity }}
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">{{ supply_item.unit_of_measure }}</span>
                                    </div>
                                </div>
                                {% if form.quantity.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.quantity.errors.0 }}
                                    </div>
                                {% endif %}
                                <p class="mt-1 text-sm text-gray-500">
                                    Enter the quantity to add, remove, or set as new total
                                </p>
                            </div>

                            <!-- Notes -->
                            <div>
                                <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Notes
                                </label>
                                <div class="mt-1">
                                    {{ form.notes }}
                                </div>
                                {% if form.notes.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.notes.errors.0 }}
                                    </div>
                                {% endif %}
                                <p class="mt-1 text-sm text-gray-500">
                                    Optional notes about this stock adjustment
                                </p>
                            </div>

                            <!-- Form Errors -->
                            {% if form.non_field_errors %}
                                <div class="rounded-md bg-red-50 p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">
                                                Please correct the following errors:
                                            </h3>
                                            <div class="mt-2 text-sm text-red-700">
                                                <ul class="list-disc pl-5 space-y-1">
                                                    {% for error in form.non_field_errors %}
                                                        <li>{{ error }}</li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                            <a href="{% url 'supply_item_detail' supply_item.pk %}" 
                               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Apply Adjustment
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Current Stock Info -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Current Stock</h3>
                    </div>
                    <div class="px-6 py-4 space-y-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gray-900">{{ supply_item.current_stock }}</div>
                            <div class="text-sm text-gray-500">{{ supply_item.unit_of_measure }} in stock</div>
                        </div>
                        
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Minimum Level:</span>
                                <span class="font-medium">{{ supply_item.minimum_stock }}</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500">Status:</span>
                                <span>
                                    {% if supply_item.is_out_of_stock %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Out of Stock
                                        </span>
                                    {% elif supply_item.is_low_stock %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                            Low Stock
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            In Stock
                                        </span>
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transaction Types Help -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Transaction Types</h3>
                    </div>
                    <div class="px-6 py-4 space-y-3">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">Stock In</div>
                                <div class="text-xs text-gray-500">Add items to inventory (deliveries, returns)</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                    <svg class="w-3 h-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">Stock Out</div>
                                <div class="text-xs text-gray-500">Remove items from inventory (issues, damage)</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">Adjustment</div>
                                <div class="text-xs text-gray-500">Set exact stock count (physical count corrections)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-focus quantity input
    document.addEventListener('DOMContentLoaded', function() {
        const quantityInput = document.querySelector('input[name="quantity"]');
        if (quantityInput) {
            quantityInput.focus();
        }
    });
</script>
{% endblock %}