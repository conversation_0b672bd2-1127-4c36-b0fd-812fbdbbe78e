{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ title }}</h1>
                    <p class="mt-1 text-sm text-gray-600">
                        {% if supply_item %}
                            Update supply item information and stock levels
                        {% else %}
                            Add a new supply item to the inventory
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{% url 'inventory_list' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Inventory
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white shadow rounded-lg">
            <form method="post" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Supply Item Information</h3>
                    <p class="mt-1 text-sm text-gray-600">
                        Fill in the details for the supply item. All fields marked with * are required.
                    </p>
                </div>

                <div class="px-6 pb-6 space-y-6">
                    <!-- Item Name -->
                    <div>
                        <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Item Name *
                        </label>
                        <div class="mt-1">
                            {{ form.name }}
                        </div>
                        {% if form.name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Description
                        </label>
                        <div class="mt-1">
                            {{ form.description }}
                        </div>
                        {% if form.description.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.description.errors.0 }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">
                            Optional description to help identify the item
                        </p>
                    </div>

                    <!-- Category and Unit of Measure -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Category *
                            </label>
                            <div class="mt-1">
                                {{ form.category }}
                            </div>
                            {% if form.category.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.category.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div>
                            <label for="{{ form.unit_of_measure.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Unit of Measure *
                            </label>
                            <div class="mt-1">
                                {{ form.unit_of_measure }}
                            </div>
                            {% if form.unit_of_measure.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.unit_of_measure.errors.0 }}
                                </div>
                            {% endif %}
                            <p class="mt-1 text-sm text-gray-500">
                                e.g., pieces, boxes, reams, liters
                            </p>
                        </div>
                    </div>

                    <!-- Stock Levels -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="{{ form.current_stock.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Current Stock *
                            </label>
                            <div class="mt-1">
                                {{ form.current_stock }}
                            </div>
                            {% if form.current_stock.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.current_stock.errors.0 }}
                                </div>
                            {% endif %}
                            <p class="mt-1 text-sm text-gray-500">
                                Current quantity in stock
                            </p>
                        </div>

                        <div>
                            <label for="{{ form.minimum_stock.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Minimum Stock Level *
                            </label>
                            <div class="mt-1">
                                {{ form.minimum_stock }}
                            </div>
                            {% if form.minimum_stock.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.minimum_stock.errors.0 }}
                                </div>
                            {% endif %}
                            <p class="mt-1 text-sm text-gray-500">
                                Alert when stock falls below this level
                            </p>
                        </div>
                    </div>

                    <!-- QR Code Info (for existing items) -->
                    {% if supply_item and supply_item.qr_code %}
                        <div>
                            <label class="block text-sm font-medium text-gray-700">
                                Current QR Code
                            </label>
                            <div class="mt-1 flex items-center space-x-4">
                                <img src="{{ supply_item.qr_code.url }}" alt="QR Code" class="h-20 w-20 border border-gray-300 rounded">
                                <div>
                                    <p class="text-sm text-gray-600">QR Code Data: {{ supply_item.qr_code_data }}</p>
                                    <p class="text-xs text-gray-500 mt-1">QR code will be regenerated automatically if needed</p>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="rounded-md bg-red-50 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">
                                        Please correct the following errors:
                                    </h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <ul class="list-disc pl-5 space-y-1">
                                            {% for error in form.non_field_errors %}
                                                <li>{{ error }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Form Actions -->
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                    <a href="{% url 'inventory_list' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        {{ submit_text }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-focus first input field
    document.addEventListener('DOMContentLoaded', function() {
        const firstInput = document.querySelector('input[type="text"], textarea, select');
        if (firstInput) {
            firstInput.focus();
        }
    });
</script>
{% endblock %}