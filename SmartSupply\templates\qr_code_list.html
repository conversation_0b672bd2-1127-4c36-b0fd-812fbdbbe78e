{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">QR Code List</h1>
            <p class="text-gray-600">Manage QR codes for all supply items</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-2 mt-4 sm:mt-0">
            <button onclick="batchGenerateQRCodes()" 
                    class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-magic mr-2"></i>Batch Generate
            </button>
            <a href="{% url 'qr_code_management' %}" 
               class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center">
                <i class="fas fa-arrow-left mr-2"></i>Back to Management
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4" hx-get="{% url 'qr_code_list' %}" hx-target="#qr-code-table" hx-trigger="change, keyup delay:500ms from:input[name='search']">
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" 
                       name="search" 
                       id="search"
                       value="{{ search_query }}"
                       placeholder="Search items..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select name="category" 
                        id="category"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                        <option value="{{ category.id }}" {% if category.id|stringformat:"s" == category_filter %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label for="qr_status" class="block text-sm font-medium text-gray-700 mb-1">QR Status</label>
                <select name="qr_status" 
                        id="qr_status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Items</option>
                    <option value="with_qr" {% if qr_status_filter == 'with_qr' %}selected{% endif %}>With QR Code</option>
                    <option value="without_qr" {% if qr_status_filter == 'without_qr' %}selected{% endif %}>Without QR Code</option>
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="submit" 
                        class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
            </div>
        </form>
    </div>

    <!-- QR Code Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div id="qr-code-table">
            {% include 'partials/qr_code_list_table.html' %}
        </div>
    </div>
</div>

<!-- Batch Generation Modal -->
<div id="batchGenerationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Batch Generate QR Codes</h3>
            <p class="text-gray-600 mb-4">Generate QR codes for all selected items or items without QR codes.</p>
            
            <div class="space-y-4">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="includeLabel" checked class="mr-2">
                        <span class="text-sm text-gray-700">Include item labels in QR codes</span>
                    </label>
                </div>
                
                <div class="flex gap-3">
                    <button onclick="generateForSelected()" 
                            class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Generate for Selected
                    </button>
                    <button onclick="generateForMissing()" 
                            class="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        Generate for Missing
                    </button>
                </div>
                
                <button onclick="closeBatchModal()" 
                        class="w-full bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Preview Modal -->
<div id="qrPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full" id="qrPreviewContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<script>
let selectedItems = new Set();

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    
    itemCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
        if (selectAllCheckbox.checked) {
            selectedItems.add(parseInt(checkbox.value));
        } else {
            selectedItems.delete(parseInt(checkbox.value));
        }
    });
    
    updateSelectedCount();
}

function toggleItemSelection(itemId) {
    if (selectedItems.has(itemId)) {
        selectedItems.delete(itemId);
    } else {
        selectedItems.add(itemId);
    }
    updateSelectedCount();
}

function updateSelectedCount() {
    const count = selectedItems.size;
    const countElement = document.getElementById('selectedCount');
    if (countElement) {
        countElement.textContent = count;
    }
}

function batchGenerateQRCodes() {
    document.getElementById('batchGenerationModal').classList.remove('hidden');
}

function closeBatchModal() {
    document.getElementById('batchGenerationModal').classList.add('hidden');
}

function generateForSelected() {
    if (selectedItems.size === 0) {
        alert('Please select at least one item');
        return;
    }
    
    const includeLabel = document.getElementById('includeLabel').checked;
    
    fetch('/api/batch-generate-qr-codes/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            item_ids: Array.from(selectedItems),
            include_label: includeLabel
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
        }
        closeBatchModal();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to generate QR codes');
        closeBatchModal();
    });
}

function generateForMissing() {
    // Get all items without QR codes
    const itemsWithoutQR = Array.from(document.querySelectorAll('.item-checkbox[data-has-qr="false"]'))
                                .map(checkbox => parseInt(checkbox.value));
    
    if (itemsWithoutQR.length === 0) {
        alert('All items already have QR codes');
        return;
    }
    
    const includeLabel = document.getElementById('includeLabel').checked;
    
    fetch('/api/batch-generate-qr-codes/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            item_ids: itemsWithoutQR,
            include_label: includeLabel
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
        }
        closeBatchModal();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to generate QR codes');
        closeBatchModal();
    });
}

function previewQRCode(itemId) {
    fetch(`/qr-codes/preview/${itemId}/`, {
        headers: {
            'HX-Request': 'true'
        }
    })
    .then(response => response.text())
    .then(html => {
        document.getElementById('qrPreviewContent').innerHTML = html;
        document.getElementById('qrPreviewModal').classList.remove('hidden');
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to load QR code preview');
    });
}

function closeQRPreview() {
    document.getElementById('qrPreviewModal').classList.add('hidden');
}

function generateQRCode(itemId) {
    fetch(`/qr-codes/generate/${itemId}/`, {
        method: 'POST',
        headers: {
            'HX-Request': 'true',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to generate QR code');
    });
}

function regenerateQRCode(itemId) {
    if (!confirm('Are you sure you want to regenerate the QR code? This will create a new QR code and invalidate the old one.')) {
        return;
    }
    
    fetch(`/qr-codes/regenerate/${itemId}/`, {
        method: 'POST',
        headers: {
            'HX-Request': 'true',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to regenerate QR code');
    });
}

// Close modals when clicking outside
document.getElementById('batchGenerationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeBatchModal();
    }
});

document.getElementById('qrPreviewModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQRPreview();
    }
});
</script>
{% endblock %}