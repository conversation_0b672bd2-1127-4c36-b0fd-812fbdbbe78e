<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Smart Supply Management System</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <style>
        /* Custom gradient background */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* Mobile-first responsive design */
        @media (max-width: 640px) {
            .register-container {
                margin: 1rem;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 bg-white rounded-lg shadow-xl p-8 register-container">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                Smart Supply Management System
            </p>
        </div>

        <!-- Messages -->
        {% if messages %}
        <div class="space-y-2">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} p-3 text-sm rounded-md {% if message.tags == 'error' %}bg-red-50 text-red-800 border border-red-200{% elif message.tags == 'success' %}bg-green-50 text-green-800 border border-green-200{% elif message.tags == 'warning' %}bg-yellow-50 text-yellow-800 border border-yellow-200{% else %}bg-blue-50 text-blue-800 border border-blue-200{% endif %}"
                 x-data="{ show: true }" 
                 x-show="show"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-90"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-300"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-90">
                <div class="flex justify-between items-center">
                    <span>{{ message }}</span>
                    <button @click="show = false" class="ml-4 text-current opacity-50 hover:opacity-75">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Registration Form -->
        <form class="mt-8 space-y-6" method="post" x-data="{ loading: false, showOptional: false }" @submit="loading = true">
            {% csrf_token %}
            
            <div class="space-y-4">
                <!-- Username Field -->
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Username <span class="text-red-500">*</span>
                    </label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.username.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    {% if form.username.help_text %}
                        <p class="mt-1 text-xs text-gray-500">{{ form.username.help_text }}</p>
                    {% endif %}
                </div>

                <!-- First Name Field -->
                <div>
                    <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        First Name <span class="text-red-500">*</span>
                    </label>
                    {{ form.first_name }}
                    {% if form.first_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.first_name.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Last Name Field -->
                <div>
                    <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Last Name <span class="text-red-500">*</span>
                    </label>
                    {{ form.last_name }}
                    {% if form.last_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.last_name.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Email Field -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.email.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Role Field -->
                <div>
                    <label for="{{ form.role.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Role <span class="text-red-500">*</span>
                    </label>
                    {{ form.role }}
                    {% if form.role.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.role.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500">Select your role in the organization</p>
                </div>

                <!-- Password Fields -->
                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Password <span class="text-red-500">*</span>
                    </label>
                    {{ form.password1 }}
                    {% if form.password1.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.password1.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    {% if form.password1.help_text %}
                        <p class="mt-1 text-xs text-gray-500">{{ form.password1.help_text }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password <span class="text-red-500">*</span>
                    </label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.password2.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    {% if form.password2.help_text %}
                        <p class="mt-1 text-xs text-gray-500">{{ form.password2.help_text }}</p>
                    {% endif %}
                </div>

                <!-- Optional Fields Toggle -->
                <div class="border-t pt-4">
                    <button type="button" 
                            @click="showOptional = !showOptional"
                            class="flex items-center text-sm text-blue-600 hover:text-blue-500">
                        <svg class="w-4 h-4 mr-1 transform transition-transform" 
                             :class="{ 'rotate-90': showOptional }"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                        <span x-text="showOptional ? 'Hide optional fields' : 'Show optional fields'"></span>
                    </button>
                </div>

                <!-- Optional Fields -->
                <div x-show="showOptional" 
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="space-y-4">
                    
                    <!-- Department Field -->
                    <div>
                        <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Department
                        </label>
                        {{ form.department }}
                        {% if form.department.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.department.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Phone Number Field -->
                    <div>
                        <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Phone Number
                        </label>
                        {{ form.phone_number }}
                        {% if form.phone_number.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.phone_number.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                        :disabled="loading">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" 
                             :class="{ 'animate-spin': loading }"
                             fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"></path>
                        </svg>
                    </span>
                    <span x-show="!loading">Create Account</span>
                    <span x-show="loading" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating account...
                    </span>
                </button>
            </div>

            <!-- Login Link -->
            <div class="text-center">
                <p class="text-sm text-gray-600">
                    Already have an account?
                    <a href="{% url 'login' %}" class="font-medium text-blue-600 hover:text-blue-500">
                        Sign in here
                    </a>
                </p>
            </div>
        </form>

        <!-- Footer -->
        <div class="text-center text-xs text-gray-500 mt-8">
            <p>&copy; 2024 JHCSC Dumingag Campus. All rights reserved.</p>
        </div>
    </div>

    <!-- Mobile-specific optimizations -->
    <script>
        // Prevent zoom on input focus for iOS
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('input[type="text"], input[type="password"], input[type="email"]');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    if (window.innerWidth < 768) {
                        document.querySelector('meta[name=viewport]').setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    }
                });
                
                input.addEventListener('blur', function() {
                    if (window.innerWidth < 768) {
                        document.querySelector('meta[name=viewport]').setAttribute('content', 'width=device-width, initial-scale=1.0');
                    }
                });
            });
        });
    </script>
</body>
</html>