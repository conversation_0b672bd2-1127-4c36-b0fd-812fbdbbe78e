from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
import json

from .models import SupplyItem, QRScanLog, InventoryTransaction, RequestItem
from .decorators import role_required
from .qr_utils import QRCodeGenerator, generate_qr_code_for_item, regenerate_qr_code_for_item, batch_generate_qr_codes_for_items


@login_required
@role_required(['gso_staff', 'admin'])
def qr_scanner(request):
    """QR code scanner interface"""
    return render(request, 'qr_scanner.html', {
        'page_title': 'QR Code Scanner'
    })


@login_required
@role_required(['gso_staff', 'admin'])
@require_http_methods(["POST"])
def process_qr_scan(request):
    """Process QR code scan and log the action"""
    try:
        data = json.loads(request.body)
        qr_code_data = data.get('qr_code_data')
        scan_type = data.get('scan_type', 'inventory_check')
        location = data.get('location', '')
        notes = data.get('notes', '')
        request_item_id = data.get('request_item_id')
        
        if not qr_code_data:
            return JsonResponse({
                'success': False,
                'message': 'QR code data is required'
            }, status=400)
        
        # Find the supply item by QR code data
        try:
            supply_item = SupplyItem.objects.get(qr_code_data=qr_code_data)
        except SupplyItem.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Supply item not found for this QR code'
            }, status=404)
        
        # Get request item if provided
        request_item = None
        if request_item_id:
            try:
                request_item = RequestItem.objects.get(id=request_item_id)
            except RequestItem.DoesNotExist:
                pass
        
        # Create scan log with enhanced metadata
        scan_log = QRScanLog.objects.create(
            supply_item=supply_item,
            scanned_by=request.user,
            scan_type=scan_type,
            location=location or 'Not specified',
            notes=notes,
            request_item=request_item
        )
        
        # Process inventory transaction based on scan type
        if scan_type == 'issuance' and request_item:
            # Decrease stock for issuance
            quantity = min(request_item.quantity_approved - request_item.quantity_released, 
                          supply_item.current_stock)
            if quantity > 0:
                previous_stock = supply_item.current_stock
                supply_item.current_stock -= quantity
                supply_item.save()
                
                # Update request item
                request_item.quantity_released += quantity
                request_item.save()
                
                # Create inventory transaction
                InventoryTransaction.objects.create(
                    supply_item=supply_item,
                    transaction_type='out',
                    quantity=quantity,
                    previous_stock=previous_stock,
                    new_stock=supply_item.current_stock,
                    performed_by=request.user,
                    reference_number=request_item.request.request_number,
                    notes=f'Issued via QR scan - {notes}'
                )
        
        elif scan_type == 'return':
            # Increase stock for returns (simplified - in real scenario, you'd need quantity)
            quantity = data.get('quantity', 1)
            previous_stock = supply_item.current_stock
            supply_item.current_stock += quantity
            supply_item.save()
            
            # Create inventory transaction
            InventoryTransaction.objects.create(
                supply_item=supply_item,
                transaction_type='in',
                quantity=quantity,
                previous_stock=previous_stock,
                new_stock=supply_item.current_stock,
                performed_by=request.user,
                notes=f'Returned via QR scan - {notes}'
            )
        
        return JsonResponse({
            'success': True,
            'message': f'QR code scanned successfully for {supply_item.name}',
            'data': {
                'supply_item': {
                    'id': supply_item.id,
                    'name': supply_item.name,
                    'current_stock': supply_item.current_stock,
                    'unit_of_measure': supply_item.unit_of_measure
                },
                'scan_log': {
                    'id': scan_log.id,
                    'scan_type': scan_log.get_scan_type_display(),
                    'scan_datetime': scan_log.scan_datetime.isoformat(),
                    'location': scan_log.location
                }
            }
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        }, status=500)


@login_required
@role_required(['gso_staff', 'admin'])
def scan_history(request):
    """Display QR scan history with filtering and search"""
    # Get filter parameters
    search_query = request.GET.get('search', '')
    scan_type_filter = request.GET.get('scan_type', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    user_filter = request.GET.get('user', '')
    
    # Base queryset
    scan_logs = QRScanLog.objects.select_related(
        'supply_item', 'scanned_by', 'request_item__request'
    ).all()
    
    # Apply filters
    if search_query:
        scan_logs = scan_logs.filter(
            Q(supply_item__name__icontains=search_query) |
            Q(location__icontains=search_query) |
            Q(notes__icontains=search_query)
        )
    
    if scan_type_filter:
        scan_logs = scan_logs.filter(scan_type=scan_type_filter)
    
    if date_from:
        scan_logs = scan_logs.filter(scan_datetime__date__gte=date_from)
    
    if date_to:
        scan_logs = scan_logs.filter(scan_datetime__date__lte=date_to)
    
    if user_filter:
        scan_logs = scan_logs.filter(scanned_by__username__icontains=user_filter)
    
    # Pagination
    paginator = Paginator(scan_logs, 20)  # Show 20 logs per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_title': 'QR Scan History',
        'page_obj': page_obj,
        'search_query': search_query,
        'scan_type_filter': scan_type_filter,
        'date_from': date_from,
        'date_to': date_to,
        'user_filter': user_filter,
        'scan_type_choices': QRScanLog.SCAN_TYPE_CHOICES,
    }
    
    # Return HTMX partial if requested
    if request.headers.get('HX-Request'):
        return render(request, 'partials/scan_history_table.html', context)
    
    return render(request, 'scan_history.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def get_supply_item_info(request, qr_code_data):
    """Get supply item information by QR code data"""
    try:
        supply_item = get_object_or_404(SupplyItem, qr_code_data=qr_code_data)
        
        # Get recent scan logs for this item
        recent_scans = QRScanLog.objects.filter(
            supply_item=supply_item
        ).select_related('scanned_by').order_by('-scan_datetime')[:5]
        
        return JsonResponse({
            'success': True,
            'data': {
                'supply_item': {
                    'id': supply_item.id,
                    'name': supply_item.name,
                    'description': supply_item.description,
                    'category': supply_item.category.name,
                    'current_stock': supply_item.current_stock,
                    'minimum_stock': supply_item.minimum_stock,
                    'unit_of_measure': supply_item.unit_of_measure,
                    'is_low_stock': supply_item.is_low_stock,
                    'is_out_of_stock': supply_item.is_out_of_stock,
                },
                'recent_scans': [
                    {
                        'scan_type': scan.get_scan_type_display(),
                        'scanned_by': scan.scanned_by.get_full_name() or scan.scanned_by.username,
                        'scan_datetime': scan.scan_datetime.strftime('%Y-%m-%d %H:%M'),
                        'location': scan.location,
                        'notes': scan.notes
                    }
                    for scan in recent_scans
                ]
            }
        })
        
    except SupplyItem.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Supply item not found'
        }, status=404)


@login_required
@role_required(['gso_staff', 'admin'])
def pending_requests_for_scan(request):
    """Get pending approved requests that can be processed via QR scan"""
    from .models import SupplyRequest
    
    pending_requests = SupplyRequest.objects.filter(
        status='approved'
    ).select_related('requester').prefetch_related(
        'items__supply_item'
    ).order_by('-approved_date')[:10]
    
    requests_data = []
    for req in pending_requests:
        items_data = []
        for item in req.items.all():
            if item.quantity_released < item.quantity_approved:
                items_data.append({
                    'id': item.id,
                    'supply_item_name': item.supply_item.name,
                    'quantity_approved': item.quantity_approved,
                    'quantity_released': item.quantity_released,
                    'remaining': item.quantity_approved - item.quantity_released,
                    'qr_code_data': item.supply_item.qr_code_data
                })
        
        if items_data:  # Only include requests with unreleased items
            requests_data.append({
                'id': req.id,
                'request_number': req.request_number,
                'requester': req.requester.get_full_name() or req.requester.username,
                'department': req.department,
                'items': items_data
            })
    
    return JsonResponse({
        'success': True,
        'data': requests_data
    })


# QR Code Management Views

@login_required
@role_required(['gso_staff', 'admin'])
def qr_code_management(request):
    """QR code management dashboard"""
    # Get statistics
    total_items = SupplyItem.objects.count()
    items_with_qr = SupplyItem.objects.exclude(qr_code='').count()
    items_without_qr = total_items - items_with_qr
    
    # Get recent QR generations (items with QR codes, ordered by creation)
    recent_qr_items = SupplyItem.objects.exclude(qr_code='').order_by('-updated_at')[:10]
    
    context = {
        'page_title': 'QR Code Management',
        'total_items': total_items,
        'items_with_qr': items_with_qr,
        'items_without_qr': items_without_qr,
        'recent_qr_items': recent_qr_items,
    }
    
    return render(request, 'qr_management.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def generate_qr_code(request, item_id):
    """Generate QR code for a specific supply item"""
    supply_item = get_object_or_404(SupplyItem, id=item_id)
    
    try:
        qr_url = generate_qr_code_for_item(supply_item, include_label=True)
        
        if request.headers.get('HX-Request'):
            return JsonResponse({
                'success': True,
                'message': f'QR code generated for {supply_item.name}',
                'qr_url': qr_url,
                'item_id': supply_item.id
            })
        else:
            messages.success(request, f'QR code generated successfully for {supply_item.name}')
            return redirect('supply_item_detail', pk=supply_item.id)
            
    except Exception as e:
        if request.headers.get('HX-Request'):
            return JsonResponse({
                'success': False,
                'message': f'Failed to generate QR code: {str(e)}'
            }, status=500)
        else:
            messages.error(request, f'Failed to generate QR code: {str(e)}')
            return redirect('supply_item_detail', pk=supply_item.id)


@login_required
@role_required(['gso_staff', 'admin'])
def regenerate_qr_code(request, item_id):
    """Regenerate QR code for a specific supply item"""
    supply_item = get_object_or_404(SupplyItem, id=item_id)
    
    try:
        qr_url = regenerate_qr_code_for_item(supply_item)
        
        if request.headers.get('HX-Request'):
            return JsonResponse({
                'success': True,
                'message': f'QR code regenerated for {supply_item.name}',
                'qr_url': qr_url,
                'item_id': supply_item.id,
                'new_qr_data': supply_item.qr_code_data
            })
        else:
            messages.success(request, f'QR code regenerated successfully for {supply_item.name}')
            return redirect('supply_item_detail', pk=supply_item.id)
            
    except Exception as e:
        if request.headers.get('HX-Request'):
            return JsonResponse({
                'success': False,
                'message': f'Failed to regenerate QR code: {str(e)}'
            }, status=500)
        else:
            messages.error(request, f'Failed to regenerate QR code: {str(e)}')
            return redirect('supply_item_detail', pk=supply_item.id)


@login_required
@role_required(['gso_staff', 'admin'])
def download_qr_code(request, item_id):
    """Download QR code image for a supply item"""
    supply_item = get_object_or_404(SupplyItem, id=item_id)
    
    if not supply_item.qr_code:
        # Generate QR code if it doesn't exist
        try:
            generate_qr_code_for_item(supply_item, include_label=True)
        except Exception as e:
            messages.error(request, f'Failed to generate QR code: {str(e)}')
            return redirect('supply_item_detail', pk=supply_item.id)
    
    try:
        # Serve the QR code image as download
        response = HttpResponse(supply_item.qr_code.read(), content_type='image/png')
        filename = f"qr_code_{supply_item.name.replace(' ', '_')}_{supply_item.id}.png"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response
        
    except Exception as e:
        messages.error(request, f'Failed to download QR code: {str(e)}')
        return redirect('supply_item_detail', pk=supply_item.id)


@login_required
@role_required(['gso_staff', 'admin'])
@require_http_methods(["POST"])
def batch_generate_qr_codes(request):
    """Generate QR codes for multiple supply items"""
    try:
        data = json.loads(request.body)
        item_ids = data.get('item_ids', [])
        include_label = data.get('include_label', True)
        
        if not item_ids:
            return JsonResponse({
                'success': False,
                'message': 'No items selected'
            }, status=400)
        
        # Get supply items
        supply_items = SupplyItem.objects.filter(id__in=item_ids)
        
        if not supply_items.exists():
            return JsonResponse({
                'success': False,
                'message': 'No valid items found'
            }, status=404)
        
        # Generate QR codes
        results = batch_generate_qr_codes_for_items(supply_items, include_label)
        
        # Count successes and failures
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful
        
        return JsonResponse({
            'success': True,
            'message': f'QR codes generated: {successful} successful, {failed} failed',
            'results': results,
            'stats': {
                'total': len(results),
                'successful': successful,
                'failed': failed
            }
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        }, status=500)


@login_required
@role_required(['gso_staff', 'admin'])
def qr_code_list(request):
    """List all supply items with QR code status"""
    # Get filter parameters
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', '')
    qr_status_filter = request.GET.get('qr_status', '')  # 'with_qr', 'without_qr', 'all'
    
    # Base queryset
    supply_items = SupplyItem.objects.select_related('category').all()
    
    # Apply filters
    if search_query:
        supply_items = supply_items.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(category__name__icontains=search_query)
        )
    
    if category_filter:
        supply_items = supply_items.filter(category_id=category_filter)
    
    if qr_status_filter == 'with_qr':
        supply_items = supply_items.exclude(qr_code='')
    elif qr_status_filter == 'without_qr':
        supply_items = supply_items.filter(qr_code='')
    
    # Pagination
    paginator = Paginator(supply_items, 20)  # Show 20 items per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get categories for filter dropdown
    from .models import SupplyCategory
    categories = SupplyCategory.objects.all().order_by('name')
    
    context = {
        'page_title': 'QR Code List',
        'page_obj': page_obj,
        'search_query': search_query,
        'category_filter': category_filter,
        'qr_status_filter': qr_status_filter,
        'categories': categories,
    }
    
    # Return HTMX partial if requested
    if request.headers.get('HX-Request'):
        return render(request, 'partials/qr_code_list_table.html', context)
    
    return render(request, 'qr_code_list.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def qr_code_preview(request, item_id):
    """Preview QR code for a supply item"""
    supply_item = get_object_or_404(SupplyItem, id=item_id)
    
    # Generate QR code if it doesn't exist
    if not supply_item.qr_code:
        try:
            generate_qr_code_for_item(supply_item, include_label=True)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Failed to generate QR code: {str(e)}'
            }, status=500)
    
    context = {
        'supply_item': supply_item,
    }
    
    if request.headers.get('HX-Request'):
        return render(request, 'partials/qr_code_preview_modal.html', context)
    
    return render(request, 'qr_code_preview.html', context)